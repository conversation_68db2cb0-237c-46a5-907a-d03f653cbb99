import { supabase } from '@/integrations/supabase/client';
import { User, PostgrestError } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';
import { toast } from 'sonner';

// Track if we've logged a table not found error
let notificationsTableErrorLogged = false;

// Reset error flag to allow retrying after fixes
export const resetNotificationErrorFlag = () => {
  notificationsTableErrorLogged = false;
};

// Types
export interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  restaurant_id: string;
  action_label?: string;
  action_url?: string;
  priority?: 'low' | 'medium' | 'high';
}

// Extend Database type to include notifications table
export type Tables = Database['public']['Tables'] & {
  notifications: {
    Row: {
      id: string;
      type: string;
      title: string;
      message: string;
      created_at: string;
      read: boolean;
      restaurant_id: string;
      action_label?: string;
      action_url?: string;
      priority?: 'low' | 'medium' | 'high';
    };
    Insert: {
      id?: string;
      type: string;
      title: string;
      message: string;
      created_at?: string;
      read?: boolean;
      restaurant_id: string;
      action_label?: string;
      action_url?: string;
      priority?: 'low' | 'medium' | 'high';
    };
    Update: {
      id?: string;
      type?: string;
      title?: string;
      message?: string;
      created_at?: string;
      read?: boolean;
      restaurant_id?: string;
      action_label?: string;
      action_url?: string;
      priority?: 'low' | 'medium' | 'high';
    };
  };
};

type DatabaseNotification = Tables['notifications']['Row'];

// Check if notifications table exists
export const checkNotificationsTableExists = async (): Promise<boolean> => {
  try {
    // Try to query the table - will fail if it doesn't exist
    const { error } = await supabase
      .from('notifications')
      .select('id')
      .limit(1);

    // If no error, table exists
    return !error;
  } catch (error) {
    console.error('Error checking notifications table:', error);
    return false;
  }
};

export const fetchNotifications = async (user: User | null): Promise<Notification[]> => {
  if (!user) return [];

  try {
    // If we've previously logged an error about missing table, skip the fetch
    if (notificationsTableErrorLogged) {
      return [];
    }

    console.log('🔔 Fetching notifications for user:', user.id);

    // Get restaurant IDs for the user through the correct architecture:
    // user -> businesses -> restaurant_details
    const { data: restaurantDetails, error: restaurantError } = await supabase
      .from('restaurant_details')
      .select(`
        id,
        businesses!inner (
          id,
          name,
          user_id
        )
      `)
      .eq('businesses.user_id', user.id);

    if (restaurantError) {
      console.error('Error fetching user restaurants:', restaurantError);
      return [];
    }

    if (!restaurantDetails || restaurantDetails.length === 0) {
      console.log('🔔 No restaurants found for user');
      return [];
    }

    const restaurantIds = restaurantDetails.map(rd => rd.id);
    console.log('🔔 Found restaurant IDs:', restaurantIds);

    // Fetch notifications for user's restaurants
    const { data: dbNotifications, error } = await supabase
      .from('notifications')
      .select('*')
      .in('restaurant_id', restaurantIds)
      .order('created_at', { ascending: false });

    if (error) {
      // Check if it's a table not found error
      if (error.code === '42P01' && !notificationsTableErrorLogged) {
        notificationsTableErrorLogged = true;
        console.error('Error fetching notifications: Table "notifications" does not exist. Please run the SQL migration.');
      } else if (!notificationsTableErrorLogged) {
        console.error('Error fetching notifications:', error);
      }
      return [];
    }

    console.log('🔔 Raw notifications from database:', dbNotifications?.length || 0);

    // Transform database notifications to application notifications
    const notifications = (dbNotifications || []).map((notification: any) => ({
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      timestamp: new Date(notification.created_at),
      read: notification.read,
      restaurant_id: notification.restaurant_id,
      action_label: notification.action_label,
      action_url: notification.action_url,
      priority: notification.priority
    }));

    console.log('🔔 Transformed notifications:', notifications.length);
    console.log('🔔 Unread notifications:', notifications.filter(n => !n.read).length);

    return notifications;
  } catch (error) {
    if (!notificationsTableErrorLogged) {
      console.error('Error in fetchNotifications:', error);
    }
    return [];
  }
};

export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId);

    return !error;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
};

export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  try {
    // Get restaurant IDs for the user through the correct architecture:
    // user -> businesses -> restaurant_details
    const { data: restaurantDetails, error: restaurantError } = await supabase
      .from('restaurant_details')
      .select(`
        id,
        businesses!inner (
          id,
          name,
          user_id
        )
      `)
      .eq('businesses.user_id', userId);

    if (restaurantError) {
      console.error('Error fetching user restaurants for mark all read:', restaurantError);
      return false;
    }

    if (!restaurantDetails || restaurantDetails.length === 0) return false;

    const restaurantIds = restaurantDetails.map(rd => rd.id);

    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .in('restaurant_id', restaurantIds)
      .eq('read', false);

    return !error;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return false;
  }
};

/**
 * Mark a single notification as read by ID.
 */
export const markAsReadNotification = async (notificationId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId);
    if (error) {
      console.error('Error in markAsReadNotification:', error);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error in markAsReadNotification:', error);
    return false;
  }
};

// Setup push notification permissions
export const setupPushNotifications = async (): Promise<boolean> => {
  if (!('Notification' in window)) {
    console.error('This browser does not support desktop notification');
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  } else if (Notification.permission === 'default') {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  // At this point, permission must be either 'denied' or 'granted'
  return false; // If we get here, permission was denied
};

// Send browser push notification
export const sendPushNotification = (
  title: string,
  options: NotificationOptions = {}
): void => {
  // Create a base options object with standard properties
  const defaultOptions: NotificationOptions = {
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    ...options
  };

  // Add vibration pattern - this is part of the Web Notification API
  // but TypeScript's DOM lib doesn't include it in NotificationOptions
  // @ts-expect-error -- Needed for vibration pattern on mobile devices
  defaultOptions.vibrate = [200, 100, 200];

  // First check if notifications are supported and permission is granted
  if (!('Notification' in window)) {
    console.warn('This browser does not support desktop notification');
    return;
  }

  if (Notification.permission === 'granted') {
    try {
      const notification = new Notification(title, defaultOptions);

      // Handle notification click
      if (options.data?.url) {
        notification.onclick = () => {
          window.focus();
          if (options.data?.url) {
            window.location.href = options.data.url;
          }
        };
      }

      // Play sound if specified
      if (options.data?.sound) {
        const audio = new Audio(options.data.sound);
        audio.volume = 0.5;
        audio.play().catch(err => console.log('Audio play failed:', err));
      }
    } catch (error) {
      console.error('Error creating notification:', error);
    }
  } else {
    console.warn('Notification permission not granted');
  }
};

// Create a new notification in the database and show push notification
export const createNotification = async (
  notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>,
  showPush: boolean = true
): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .insert({
        ...notificationData,
        created_at: new Date().toISOString(),
        read: false
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      return null;
    }

    // Show push notification if requested
    if (showPush) {
      sendPushNotification(notificationData.title, {
        body: notificationData.message,
        data: {
          url: notificationData.action_url,
          sound: notificationData.type === 'order' ? '/sounds/new-order.mp3' : undefined
        },
        requireInteraction: notificationData.priority === 'high'
      });

      // Also show toast notification as a fallback
      toast[notificationData.type === 'info' ? 'info' :
           notificationData.type === 'alert' ? 'error' :
           notificationData.type === 'promo' ? 'success' : 'info'](
        notificationData.title,
        {
          description: notificationData.message,
          duration: notificationData.priority === 'high' ? 10000 : 5000,
          action: notificationData.action_label ? {
            label: notificationData.action_label,
            onClick: () => {
              if (notificationData.action_url) {
                window.location.href = notificationData.action_url;
              }
            }
          } : undefined
        }
      );
    }

    return data.id;
  } catch (error) {
    console.error('Error in createNotification:', error);
    return null;
  }
};

// Create a notification using a server-side function (bypasses RLS)
// This is used for creating notifications from customer actions like placing orders
// No authentication required - works with anonymous users (customers)
export const createServerNotification = async (
  notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>
): Promise<string | null> => {
  try {
    console.log('Creating server notification for restaurant:', notificationData.restaurant_id);

    // Use a server-side function to create the notification
    // This bypasses RLS policies since it runs with service_role permissions
    const { data, error } = await supabase.functions.invoke('create-notification', {
      body: {
        notification: {
          ...notificationData,
          created_at: new Date().toISOString(),
          read: false
        }
      }
    });

    if (error) {
      console.error('Error creating server notification:', error);

      // Try a different approach - direct API call to the function
      try {
        console.log('Attempting direct API call to create-notification function');

        // Get the Supabase URL from the client
        const supabaseUrl = supabase.supabaseUrl;
        const functionUrl = `${supabaseUrl}/functions/v1/create-notification`;

        // Make a direct fetch call to the function
        const response = await fetch(functionUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabase.supabaseKey}`
          },
          body: JSON.stringify({
            notification: {
              ...notificationData,
              created_at: new Date().toISOString(),
              read: false
            }
          })
        });

        if (!response.ok) {
          throw new Error(`Function call failed with status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Direct function call succeeded:', result);
        return result.id;
      } catch (directCallError) {
        console.error('Direct function call also failed:', directCallError);

        // Last resort: Try to create notification directly with anon key
        // This will only work if there's an RLS policy allowing it
        try {
          console.log('Attempting last resort direct insert');
          const { data: insertData, error: insertError } = await supabase
            .from('notifications')
            .insert({
              ...notificationData,
              created_at: new Date().toISOString(),
              read: false
            })
            .select('id')
            .single();

          if (insertError) {
            console.error('Last resort insert also failed:', insertError);
            return null;
          }

          return insertData.id;
        } catch (fallbackError) {
          console.error('Error in last resort notification creation:', fallbackError);
          return null;
        }
      }
    }

    console.log('Server notification created successfully:', data);
    return data?.id || null;
  } catch (error) {
    console.error('Error in createServerNotification:', error);
    return null;
  }
};
