import { MenuItemType } from '@/components/MenuItem';
import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId } from './restaurantDbService';
import { fetchMenus } from './menuDbService';
import { applyDynamicPricing, isDynamicPricingEnabled } from './dynamicPricingService';
import { menuLogger, pricingLogger } from '@/utils/debugLogger';

// Helper function to map database data to MenuItemType
const mapToMenuItemType = (item: {
  id: string;
  name: string;
  description?: string;
  current_price?: number;
  base_price: number;
  image_url?: string;
  category: string;
  allergens?: string[];
  calories?: number;
  price_factor?: number;
}): MenuItemType => {
  // Store the original price as base_price for dynamic pricing display
  const basePrice = item.base_price;
  const currentPrice = item.current_price || item.base_price;
  const isDynamicallyPriced = item.current_price !== undefined && item.current_price !== item.base_price;

  // Map image_url to image property - ensure it's properly set with download=true parameter
  let imageUrl = item.image_url || undefined;

  // Add download=true parameter to Supabase URLs if not already present
  if (imageUrl && imageUrl.includes('supabase.co') && !imageUrl.includes('download=true')) {
    imageUrl = imageUrl.includes('?')
      ? `${imageUrl}&download=true`
      : `${imageUrl}?download=true`;
  }

  // Debug log for image URL
  console.log(`Menu item ${item.name} image URL:`, imageUrl);

  return {
    id: item.id,
    name: item.name,
    description: item.description || '',
    price: currentPrice,
    base_price: basePrice,  // Keep original price for comparison
    price_factor: item.price_factor,
    image: imageUrl,  // Ensure image property is set correctly with download=true
    category: item.category,
    allergies: item.allergens || [],
    calories: item.calories || undefined,
    isDynamicallyPriced: isDynamicallyPriced
  };
};

// Function to fetch menu items from database with time-based filtering
export const fetchMenuItems = async (tableId?: string): Promise<MenuItemType[]> => {
  // Detect if we're in customer view - Enhanced to catch all relevant patterns
  const isCustomerView = typeof window !== 'undefined' && (
    window.location.pathname.includes('/menu') ||
    window.location.pathname.includes('/table') ||
    // Add patterns that match MenuWithExpandableCards and other customer views
    window.location.search.includes('tableId=') ||
    window.location.search.includes('table=') ||
    // Catch additional customer view URLs that might not follow the exact pattern
    window.location.pathname === '/' && (
      window.location.search.includes('restaurantId=') ||
      window.location.search.includes('id=')
    )
  );

  // Get the actual URL for debugging
  const currentUrl = typeof window !== 'undefined' ? window.location.href : 'unknown';
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : 'unknown';
  const currentSearch = typeof window !== 'undefined' ? window.location.search : 'unknown';

  const viewType = isCustomerView ? 'CUSTOMER_VIEW' : 'ADMIN_VIEW';

  // Log detailed URL information to help with debugging view detection
  menuLogger.log('View detection', {
    isCustomerView,
    viewType,
    currentUrl,
    currentPath,
    currentSearch,
    timestamp: new Date().toISOString()
  });

  // Start a new debug log section
  menuLogger.section(`fetchMenuItems (${viewType})`);
  menuLogger.log('Function called with params', { tableId });

  try {
    // Get the restaurant ID based on table ID if provided, otherwise use default
    console.log('Fetching menu items for table ID:', tableId);
    menuLogger.log('Fetching menu items for table ID', { tableId });

    const restaurantId = await getRestaurantId(null, tableId);
    console.log('Using restaurant ID for menu items:', restaurantId);
    menuLogger.log('Using restaurant ID for menu items', { restaurantId });

    // Validate restaurant ID
    if (!restaurantId) {
      console.error('No valid restaurant ID found');
      menuLogger.error('No valid restaurant ID found');
      // Return an empty array if no valid restaurant ID is found
      // This ensures menu items are only displayed for valid tableIds
      return [];
    }

    // Get current time in HH:MM format
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    console.log('Current time for menu filtering:', currentTime);

    // First, get all available menus for this restaurant
    // We need to pass a user object to fetchMenus, but we don't have one here
    // Instead, we'll create a custom function to fetch menus by restaurant ID
    const { data: menus, error } = await supabase
      .from('menus')
      .select('*')
      .eq('restaurant_id', restaurantId)
      .order('name');

    if (error) {
      console.error('Error fetching menus for restaurant:', error);
      return [];
    }

    if (!menus || menus.length === 0) {
      console.log('No menus found for restaurant ID:', restaurantId);
      return [];
    }

    // Filter menus based on time
    const availableMenuIds = menus
      .filter(menu => {
        // If it's a drink menu (identified by name containing 'drink' or 'beverage'), it's always available
        if (menu.name && (menu.name.toLowerCase().includes('drink') ||
                          menu.name.toLowerCase().includes('beverage') ||
                          menu.name.toLowerCase().includes('bebida'))) {
          return true;
        }

        // The menu object might have time fields but they're not defined in the type
        // Use indexed access to safely access these properties
        const startTime = (menu as Record<string, unknown>)['start_time'] as string | undefined;
        const endTime = (menu as Record<string, unknown>)['end_time'] as string | undefined;

        // If no start_time or end_time, it's always available
        if (!startTime || !endTime) return true;

        // Check if current time is within menu's time range
        return isTimeInRange(currentTime, startTime, endTime);
      })
      .map(menu => menu.id);

    console.log('Available menu IDs based on time:', availableMenuIds);

    if (availableMenuIds.length === 0) {
      console.log('No menus available at current time');
      return [];
    }

    // Fetch menu items from database for available menus
    const { data, error: menuItemsError } = await supabase
      .from('menu_items')
      .select(`
        *,
        menus!inner (
          id,
          name,
          restaurant_id
        )
      `)
      .eq('menus.restaurant_id', restaurantId)
      .in('menu_id', availableMenuIds)
      .eq('is_available', true);

    if (menuItemsError) throw menuItemsError;

    // Map to MenuItemType
    let menuItems = (data || []).map(mapToMenuItemType);
    console.log(`Found ${menuItems.length} menu items for current time`);

    // We need to check if dynamic pricing is enabled from the database
    // Always use the database as the source of truth
    console.log('💼 Checking dynamic pricing status from database only');
    console.log('🔍 CRITICAL DEBUG: About to call isDynamicPricingEnabled with restaurantId:', restaurantId);
    menuLogger.log('Checking if dynamic pricing is enabled (critical section)');

    // Check if dynamic pricing is enabled for this restaurant
    const dynamicPricingEnabled = await isDynamicPricingEnabled(restaurantId);

    console.log('🔍 CRITICAL DEBUG: isDynamicPricingEnabled returned:', dynamicPricingEnabled);
    console.log('🔍 CRITICAL DEBUG: restaurantId used:', restaurantId);

    // Log the result of the dynamic pricing check - CRITICAL FOR DEBUGGING
    menuLogger.log('Dynamic pricing enabled check result', {
      dynamicPricingEnabled,
      restaurantId,
      isCustomerView,
      viewType,
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      timestamp: new Date().toISOString()
    });

    // Apply dynamic pricing if enabled
    if (dynamicPricingEnabled) {
      console.log('🏷️ Dynamic pricing is enabled, applying AI-based price adjustments');
      menuLogger.log('Dynamic pricing is ENABLED, preparing to apply price adjustments');

      // Convert MenuItemType to the format expected by applyDynamicPricing
      const pricingMenuItems = menuItems.map(item => ({
        id: item.id,
        name: item.name,
        category: item.category,
        price: item.price,
        base_price: item.base_price || item.price, // Ensure base_price is passed
        description: item.description,
        image: item.image,
        allergies: item.allergies,
        calories: item.calories
      }));

      console.log('⚙️ Sending items for dynamic pricing calculation...', pricingMenuItems.length);
      menuLogger.log('Sending items for dynamic pricing calculation', {
        count: pricingMenuItems.length,
        firstItem: pricingMenuItems.length > 0 ? {
          id: pricingMenuItems[0].id,
          name: pricingMenuItems[0].name,
          price: pricingMenuItems[0].price,
          base_price: pricingMenuItems[0].base_price
        } : null
      });

      console.log('💯 VERY IMPORTANT DEBUG: About to apply dynamic pricing');
      menuLogger.section('CRITICAL POINT: Applying dynamic pricing');

      // Record state before dynamic pricing call
      menuLogger.log('State before applyDynamicPricing call', {
        sampleItemBefore: menuItems.length > 0 ? {
          id: menuItems[0].id,
          name: menuItems[0].name,
          price: menuItems[0].price,
          base_price: menuItems[0].base_price
        } : null
      });

      // Apply pricing and map back to MenuItemType
      console.log(`🔄 Applying dynamic pricing for ${viewType}`, {
        url: currentUrl,
        isCustomerView
      });
      menuLogger.log('Customer view status when applying dynamic pricing', {
        isCustomerView,
        viewType,
        path: currentPath,
        search: currentSearch
      });

      const pricedItems = await applyDynamicPricing(pricingMenuItems, restaurantId, isCustomerView);

      // Log the results from dynamic pricing - CRITICAL FOR DEBUGGING
      menuLogger.log('Results returned from applyDynamicPricing', {
        success: !!pricedItems,
        itemCount: pricedItems ? pricedItems.length : 0,
        sampleItemAfter: pricedItems && pricedItems.length > 0 ? {
          id: pricedItems[0].id,
          name: pricedItems[0].name,
          price: pricedItems[0].price,
          base_price: pricedItems[0].base_price,
          price_factor: pricedItems[0].price_factor || 1.0,
          isDynamicallyPriced: pricedItems[0].isDynamicallyPriced
        } : null
      });

      // Now map the priced items back to MenuItemType
      menuItems = pricedItems.map(item => {
        // Debug log for image in dynamic pricing
        console.log(`Dynamic pricing: Menu item ${item.name} image:`, item.image);

        const mappedItem = {
          ...mapToMenuItemType({
            id: item.id,
            name: item.name,
            description: item.description || '',
            current_price: item.price,
            base_price: item.base_price || 0,
            image_url: item.image || '',  // Make sure image_url is set from item.image (download=true already added)
            category: item.category || 'food',
            allergens: item.allergies || [], // Fixed from allergens to allergies
            calories: item.calories || 0,
          }),
          isDynamicallyPriced: true,
          price_factor: item.price_factor || 1.0
        };

        // Log a sample item transformation
        if (item.id === pricedItems[0].id) {
          menuLogger.log('Sample item transformation', {
            before: {
              name: item.name,
              basePrice: item.base_price,
              price: item.price,
              factor: item.price_factor || 1.0
            },
            after: {
              name: mappedItem.name,
              basePrice: mappedItem.base_price,
              price: mappedItem.price,
              factor: mappedItem.price_factor
            },
            percentChange: item.price_factor ?
              `${((item.price_factor - 1) * 100).toFixed(1)}%` : '0%'
          });
        }

        return mappedItem;
      });

      console.log(`Applied dynamic pricing to ${menuItems.length} items (${viewType})`);
      menuLogger.log(`Applied dynamic pricing to ${menuItems.length} items`, {
        timestamp: new Date().toISOString(),
        appliedCount: menuItems.length,
        viewType,
        url: typeof window !== 'undefined' ? window.location.href : 'unknown'
      });
    } else {
      // If dynamic pricing is disabled, ensure we're using base prices
      console.log('Dynamic pricing is DISABLED, using base prices');
      menuLogger.log('Dynamic pricing is DISABLED, using base prices', {
        reason: 'isDynamicPricingEnabled returned false',
        viewType,
        timestamp: new Date().toISOString()
      });

      menuItems = menuItems.map(item => ({
        ...item,
        price: item.base_price || item.price,
        isDynamicallyPriced: false,
        price_factor: 1.0
      }));
    }

    return menuItems;
  } catch (error) {
    console.error('Error fetching menu items from database:', error);
    return [];
  }
};

// Helper function to check if a time is within a range
const isTimeInRange = (time: string, startTime: string, endTime: string): boolean => {
  // Handle overnight ranges (e.g., 22:00 to 04:00)
  if (startTime > endTime) {
    return time >= startTime || time <= endTime;
  }
  // Normal range
  return time >= startTime && time <= endTime;
};

// Function to fetch menu items by category with time-based filtering
export const fetchMenuItemsByCategory = async (category: string, tableId?: string): Promise<MenuItemType[]> => {
  try {
    // Get all menu items with time filtering
    const allMenuItems = await fetchMenuItems(tableId);

    // Filter by category
    return allMenuItems.filter(item => item.category === category);
  } catch (error) {
    console.error('Error fetching menu items by category from database:', error);
    return [];
  }
};
